"use strict";
// File generated from our OpenAPI spec
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceRenderingTemplates = void 0;
const StripeResource_js_1 = require("../StripeResource.js");
const stripeMethod = StripeResource_js_1.StripeResource.method;
exports.InvoiceRenderingTemplates = StripeResource_js_1.StripeResource.extend({
    retrieve: stripeMethod({
        method: 'GET',
        fullPath: '/v1/invoice_rendering_templates/{template}',
    }),
    list: stripeMethod({
        method: 'GET',
        fullPath: '/v1/invoice_rendering_templates',
        methodType: 'list',
    }),
    archive: stripeMethod({
        method: 'POST',
        fullPath: '/v1/invoice_rendering_templates/{template}/archive',
    }),
    unarchive: stripeMethod({
        method: 'POST',
        fullPath: '/v1/invoice_rendering_templates/{template}/unarchive',
    }),
});
