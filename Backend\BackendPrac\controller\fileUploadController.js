// controller/fileUploadController.js
const { 
  uploadBufferToCloudinary, 
  uploadToCloudinary, 
  deleteFromCloudinary 
} = require('../utils/Cloudinary');
const CreateUser = require('../model/rawDataModel');
const fs = require('fs');
const path = require('path');

// Upload single file to Cloudinary
exports.uploadSingleFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Upload to Cloudinary using buffer
    const result = await uploadBufferToCloudinary(req.file.buffer, {
      folder: 'user-uploads',
      public_id: `${Date.now()}-${req.file.originalname.split('.')[0]}`,
      resource_type: 'auto'
    });

    res.status(200).json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        url: result.secure_url,
        public_id: result.public_id,
        format: result.format,
        resource_type: result.resource_type,
        bytes: result.bytes,
        width: result.width,
        height: result.height
      }
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload file',
      error: error.message
    });
  }
};

// Upload multiple files to Cloudinary
exports.uploadMultipleFiles = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }

    const uploadPromises = req.files.map(async (file) => {
      return await uploadBufferToCloudinary(file.buffer, {
        folder: 'user-uploads',
        public_id: `${Date.now()}-${file.originalname.split('.')[0]}`,
        resource_type: 'auto'
      });
    });

    const results = await Promise.all(uploadPromises);

    const uploadedFiles = results.map(result => ({
      url: result.secure_url,
      public_id: result.public_id,
      format: result.format,
      resource_type: result.resource_type,
      bytes: result.bytes,
      width: result.width,
      height: result.height
    }));

    res.status(200).json({
      success: true,
      message: 'Files uploaded successfully',
      data: uploadedFiles
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload files',
      error: error.message
    });
  }
};

// Upload user profile picture
exports.uploadProfilePicture = async (req, res) => {
  try {
    const userId = req.params.userId || req.user?.id; // Get from params or auth middleware

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No profile picture uploaded'
      });
    }

    // Find user
    const user = await CreateUser.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Delete old profile picture if exists
    if (user.profilePicture && user.profilePicturePublicId) {
      try {
        await deleteFromCloudinary(user.profilePicturePublicId);
      } catch (deleteError) {
        console.error('Error deleting old profile picture:', deleteError);
      }
    }

    // Upload new profile picture
    const result = await uploadBufferToCloudinary(req.file.buffer, {
      folder: 'profile-pictures',
      public_id: `profile-${userId}-${Date.now()}`,
      transformation: [
        { width: 300, height: 300, crop: 'fill', gravity: 'face' },
        { quality: 'auto', fetch_format: 'auto' }
      ]
    });

    // Update user with new profile picture
    const updatedUser = await CreateUser.findByIdAndUpdate(
      userId,
      {
        profilePicture: result.secure_url,
        profilePicturePublicId: result.public_id
      },
      { new: true }
    ).select('-password'); // Exclude password from response

    res.status(200).json({
      success: true,
      message: 'Profile picture updated successfully',
      data: {
        user: updatedUser,
        profilePicture: {
          url: result.secure_url,
          public_id: result.public_id
        }
      }
    });

  } catch (error) {
    console.error('Profile picture upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload profile picture',
      error: error.message
    });
  }
};

// Delete file from Cloudinary
exports.deleteFile = async (req, res) => {
  try {
    const { publicId } = req.params;
    const { resourceType = 'image' } = req.query;

    if (!publicId) {
      return res.status(400).json({
        success: false,
        message: 'Public ID is required'
      });
    }

    const result = await deleteFromCloudinary(publicId, resourceType);

    if (result.result === 'ok') {
      res.status(200).json({
        success: true,
        message: 'File deleted successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to delete file'
      });
    }

  } catch (error) {
    console.error('Delete error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete file',
      error: error.message
    });
  }
};

// Get file info
exports.getFileInfo = async (req, res) => {
  try {
    const { publicId } = req.params;

    if (!publicId) {
      return res.status(400).json({
        success: false,
        message: 'Public ID is required'
      });
    }

    // You can implement Cloudinary admin API to get file details
    // For now, we'll return a simple response
    res.status(200).json({
      success: true,
      message: 'File info retrieved',
      data: {
        public_id: publicId,
        // Add more file details as needed
      }
    });

  } catch (error) {
    console.error('Get file info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get file info',
      error: error.message
    });
  }
};
