const express = require('express');
const router = express.Router();
const {UserData} = require('../controller/rawDataController');
const {CreateNewUser,deleteuser,GetAllUser,UpdateUser,AuthRoute,UloadFile} = require('../controller/rawDataController');
const { AuthMiddleware } = require('../Middleware/AuthMiddleware');
const upload = require('../Middleware/Multer');

router.get('/userData/:name',UserData);
router.get('/alluser',AuthMiddleware,GetAllUser);
router.post('/createUser',CreateNewUser);
router.delete('/deleteUser/:User_id',deleteuser);
router.put('/updateUSer/:User_id',UpdateUser)
router.post('/login',AuthRoute);
router.post('/upload', upload.single('file'), UloadFile)

module.exports = router;