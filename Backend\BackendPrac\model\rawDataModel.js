const mongoose = require('mongoose');

const CreateUser = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true,
        unique: true
    },
    password: {
        type: String,
        required: true
    },
    profilePicture: {
        type: String,
        default: null
    },
    profilePicturePublicId: {
        type: String,
        default: null
    },
    documents: [{
        name: String,
        url: String,
        publicId: String,
        uploadDate: {
            type: Date,
            default: Date.now
        }
    }]
}, {timestamps: true});

module.exports = mongoose.model('User',CreateUser);