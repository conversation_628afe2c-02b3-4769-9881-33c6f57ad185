
const CreateUser = require('../model/rawDataModel');
const bcrypt = require('bcrypt');
const jwt =  require('jsonwebtoken')

const User = [{
    name: 'Atif',
    email: '<EMAIL>',
    password: '123456',

},
{
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
},
{
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
}
]

exports.GetAllUser = async (req, res) => {
    try {
        const alluser = await CreateUser.find();
        res.json({ user: alluser })
    } catch (e) {
        console.log(e);
    }
}


exports.UserData = async (req, res) => {
    console.log(req.params);

    let user = User.filter((user) => user.name == req.params.name);
    if (user.length > 0) {
        res.send({ msg: 'filter Data', data: user });
    } else {
        res.send({ msg: 'No Data Found' });
    }
}

exports.CreateNewUser = async (req, res) => {
    try {
        const { name, email, password } = req.body;
        console.log(name, email, password);

        const encryptPassword = await bcrypt.hash(password, 10);
        const newUser = await CreateUser.create({ name, email, password: encryptPassword });
   console.log(newUser);
        return res.status(201).json({ msg: 'User Created', data: newUser });

    } catch (e) {
        console.log(`Error is : ` + e);
    }

}

exports.deleteuser = async (req, res) => {
    try {
        const id = req.params.User_id;
        const deleteuser = await CreateUser.findByIdAndDelete(id)
        if (!deleteuser) {
            return res.status(404).json({ msg: 'User Not Found' });
        }
        return res.status(200).json({ msg: 'User Deleted', user: deleteuser });
    }
    catch (err) {
        console.log(err);

    }
}

exports.UpdateUser = async (req, res) => {
    try {
        const id = req.params.User_id;
        const Updateuser = await CreateUser.findByIdAndUpdate(id, req.body, { new: true });
        if (!Updateuser) {
            return res.status(404).json({ msg: 'User Not Found' });
        }
        return res.status(200).json({ msg: 'User Updated', NewUser: Updateuser });
    } catch (e) {
        console.log(e);
    }
}

exports.AuthRoute = async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email) {
            return res.status(400).json({ msg: 'Email is required' });
        }
        if (!password) {
            return res.status(400).json({ msg: 'password is required' });

        }
        const find_User = await CreateUser.findOne({ email })

        if (!find_User) {
            return res.status(400).json({ msg: 'user not found or invalid email' });
        }


        const matchPassword = await bcrypt.compare(password, find_User.password);
        if (!matchPassword) {
            return res.status(400).json({ msg: 'Invalid Password' });
        }
        const token = await jwt.sign({
             id: find_User._id,
             email: find_User.email,
              name: find_User.name
            },
            "axzsndhsj12343563-+}{\@#$%&*'/?",
             { expiresIn: '1d' });



        return res.status(200).json({ msg: 'User Found', user: find_User,Token:token });

    } catch (e) {
        console.log(e);
    }
}

exports.UloadFile = (req, res) => {
    try {

        const { file } = req.file;
        console.log(file);
        return res.status(200).json({ msg: 'File Uploaded' ,fileIs:file});
    } catch (e) {
        console.log(e);
    }
}
