// Simple test script to verify payment endpoints
const https = require('http');

const testPaymentIntent = () => {
    const data = JSON.stringify({
        amount: 2000,
        currency: 'usd',
        description: 'Test payment'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/v0/payment/create-payment-intent',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': data.length
        }
    };

    const req = https.request(options, (res) => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Headers: ${JSON.stringify(res.headers)}`);
        
        let body = '';
        res.on('data', (chunk) => {
            body += chunk;
        });
        
        res.on('end', () => {
            console.log('Response Body:');
            console.log(body);
        });
    });

    req.on('error', (e) => {
        console.error(`Problem with request: ${e.message}`);
    });

    req.write(data);
    req.end();
};

console.log('Testing payment endpoint...');
testPaymentIntent();
