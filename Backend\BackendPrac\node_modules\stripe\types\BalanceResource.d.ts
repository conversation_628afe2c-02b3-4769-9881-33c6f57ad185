// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    interface BalanceRetrieveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    class BalanceResource {
      /**
       * Retrieves the current account balance, based on the authentication that was used to make the request.
       *  For a sample request, see [Accounting for negative balances](https://stripe.com/docs/connect/account-balances#accounting-for-negative-balances).
       */
      retrieve(
        params?: BalanceRetrieveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Balance>>;
      retrieve(
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.Balance>>;
    }
  }
}
