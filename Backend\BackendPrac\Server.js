require('dotenv').config();
const exp = require('express');
const app = exp();
const cors = require('cors')
const ConnectDb = require('./config/Db');
const  Route = require('./route/rawDataRoute');
const paymentRoute = require('./route/paymentRoute');

// Test route to verify server is working
app.get('/', (req, res) => {
    res.json({ message: 'Server is running!', timestamp: new Date().toISOString() });
});

// Test route for payment API
app.get('/api/v0/payment/test', (req, res) => {
    res.json({ message: 'Payment API is working!', routes: ['create-payment-intent', 'confirm-payment', 'create-customer', 'payment-history', 'test-payment'] });
});

app.use(cors());
app.use(exp.json());
app.use('/api/v0/data',Route);
app.use('/api/v0/payment', paymentRoute);
app.listen(3000, () => {
    ConnectDb();
    console.log('Server started');
});