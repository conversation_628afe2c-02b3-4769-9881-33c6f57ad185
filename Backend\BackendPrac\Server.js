const exp = require('express');
const app = exp();
const cors = require('cors')
const ConnectDb = require('./config/Db');
const  Route = require('./route/rawDataRoute');
const paymentRoute = require('./route/paymentRoute');

// app.get('/', (req, res) => {
//     res.send('Hello World!');
// });
app.use(cors());
app.use(exp.json());
app.use('/api/v0/data',Route);
app.use('/api/v0/payment', paymentRoute);
app.listen(3000, () => {
    ConnectDb();
    console.log('Server started');
});