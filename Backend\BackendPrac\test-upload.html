<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .upload-form {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>File Upload Test</h1>
    
    <!-- Single File Upload -->
    <div class="upload-form">
        <h3>Upload Single File</h3>
        <form id="singleFileForm" enctype="multipart/form-data">
            <input type="file" name="file" accept="image/*,.pdf,.doc,.docx,.txt" required>
            <br>
            <button type="submit">Upload Single File</button>
        </form>
    </div>

    <!-- Multiple Files Upload -->
    <div class="upload-form">
        <h3>Upload Multiple Files</h3>
        <form id="multipleFilesForm" enctype="multipart/form-data">
            <input type="file" name="files" accept="image/*,.pdf,.doc,.docx,.txt" multiple required>
            <br>
            <button type="submit">Upload Multiple Files</button>
        </form>
    </div>

    <div id="result"></div>

    <script>
        const API_BASE = 'http://localhost:3000'; // Change this to your server port

        // Single file upload
        document.getElementById('singleFileForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = e.target.querySelector('input[type="file"]');
            formData.append('file', fileInput.files[0]);

            try {
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                displayResult(result, response.ok);
            } catch (error) {
                displayResult({ msg: 'Network error', error: error.message }, false);
            }
        });

        // Multiple files upload
        document.getElementById('multipleFilesForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = e.target.querySelector('input[type="file"]');
            
            for (let i = 0; i < fileInput.files.length; i++) {
                formData.append('files', fileInput.files[i]);
            }

            try {
                const response = await fetch(`${API_BASE}/upload-multiple`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                displayResult(result, response.ok);
            } catch (error) {
                displayResult({ msg: 'Network error', error: error.message }, false);
            }
        });

        function displayResult(result, isSuccess) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <h4>${isSuccess ? 'Success!' : 'Error!'}</h4>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
        }
    </script>
</body>
</html>
