# File Upload with <PERSON><PERSON> and Cloudinary

This project now includes simple file upload functionality using Multer and Cloudinary.

## What was added:

### 1. Controller Functions (`controller/rawDataController.js`)
- `uploadFile` - Handles single file upload
- `uploadMultipleFiles` - Handles multiple files upload

### 2. Routes (`route/rawDataRoute.js`)
- `POST /upload` - Upload single file
- `POST /upload-multiple` - Upload multiple files (max 5)

### 3. Multer Middleware (`Middleware/Multer.js`)
- Configured for disk storage in `tmp/images` folder
- File size limit: 5MB
- Allowed file types: jpeg, jpg, png, gif, pdf, doc, docx, txt
- Generates unique filenames

### 4. Cloudinary Configuration (`utils/Cloudinary.js`)
- Already configured with your credentials
- Uploads files to 'images' folder in Cloudinary

## How to test:

### Option 1: Using the HTML test file
1. Start your server: `npm start`
2. Open `test-upload.html` in your browser
3. Select files and upload

### Option 2: Using Postman
1. **Single file upload:**
   - Method: POST
   - URL: `http://localhost:3000/upload`
   - Body: form-data
   - Key: `file` (type: File)
   - Value: Select your file

2. **Multiple files upload:**
   - Method: POST
   - URL: `http://localhost:3000/upload-multiple`
   - Body: form-data
   - Key: `files` (type: File, multiple files)
   - Value: Select multiple files

### Option 3: Using curl
```bash
# Single file
curl -X POST -F "file=@/path/to/your/file.jpg" http://localhost:3000/upload

# Multiple files
curl -X POST -F "files=@/path/to/file1.jpg" -F "files=@/path/to/file2.pdf" http://localhost:3000/upload-multiple
```

## Response Format:

### Success Response:
```json
{
  "msg": "File uploaded successfully",
  "data": {
    "url": "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/images/filename.jpg",
    "public_id": "images/filename"
  }
}
```

### Error Response:
```json
{
  "msg": "File upload failed",
  "error": "Error message"
}
```

## File Structure:
```
Backend/BackendPrac/
├── tmp/images/          # Temporary storage for uploaded files
├── controller/
│   └── rawDataController.js  # Contains upload functions
├── route/
│   └── rawDataRoute.js      # Contains upload routes
├── Middleware/
│   └── Multer.js           # Multer configuration
├── utils/
│   └── Cloudinary.js       # Cloudinary configuration
└── test-upload.html        # Test file for uploads
```

## Important Notes:
1. Make sure your `.env` file has correct Cloudinary credentials
2. The `tmp/images` folder is created automatically
3. Files are temporarily stored locally, then uploaded to Cloudinary
4. Local files are automatically deleted after Cloudinary upload
5. Maximum file size is 5MB
6. Maximum 5 files for multiple upload

## Environment Variables:
Make sure your `.env` file has:
```
CloudName=your_cloud_name
Api_key=your_api_key
Api_secret=your_api_secret
```
