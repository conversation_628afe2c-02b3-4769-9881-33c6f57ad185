# Frontend Stripe Test Payment Integration

## Quick Setup for Testing

### 1. Your Stripe Keys (Already Configured)
- **Publishable Key:** `pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4`
- **Backend URL:** `http://localhost:3000`

### 2. Install Stripe in Your Frontend
```bash
npm install @stripe/stripe-js @stripe/react-stripe-js
```

### 3. Simple Test Payment Component (React)

```jsx
import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

// Initialize Stripe
const stripePromise = loadStripe('pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4');

// Payment Form Component
const PaymentForm = () => {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    if (!stripe || !elements) {
      return;
    }

    // 1. Create payment intent from your backend
    const response = await fetch('http://localhost:3000/api/v0/payment/test-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: 1000 // $10.00 in cents
      })
    });

    const { data } = await response.json();
    const { clientSecret } = data;

    // 2. Confirm payment with Stripe
    const result = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: elements.getElement(CardElement),
        billing_details: {
          name: 'Test Customer',
        },
      }
    });

    if (result.error) {
      setMessage(`Payment failed: ${result.error.message}`);
    } else {
      setMessage('Payment succeeded!');
    }

    setLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} style={{ maxWidth: '400px', margin: '50px auto' }}>
      <h2>Test Stripe Payment</h2>
      
      <div style={{ padding: '10px', border: '1px solid #ccc', marginBottom: '20px' }}>
        <CardElement />
      </div>
      
      <button 
        type="submit" 
        disabled={!stripe || loading}
        style={{
          width: '100%',
          padding: '12px',
          backgroundColor: '#5469d4',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          fontSize: '16px'
        }}
      >
        {loading ? 'Processing...' : 'Pay $10.00'}
      </button>
      
      {message && (
        <div style={{ 
          marginTop: '20px', 
          padding: '10px', 
          backgroundColor: message.includes('succeeded') ? '#d4edda' : '#f8d7da',
          color: message.includes('succeeded') ? '#155724' : '#721c24',
          borderRadius: '4px'
        }}>
          {message}
        </div>
      )}
    </form>
  );
};

// Main App Component
const App = () => {
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm />
    </Elements>
  );
};

export default App;
```

### 4. Simple JavaScript (No React) Version

```html
<!DOCTYPE html>
<html>
<head>
    <title>Stripe Test Payment</title>
    <script src="https://js.stripe.com/v3/"></script>
</head>
<body>
    <div style="max-width: 400px; margin: 50px auto;">
        <h2>Test Stripe Payment</h2>
        
        <form id="payment-form">
            <div id="card-element" style="padding: 10px; border: 1px solid #ccc; margin-bottom: 20px;">
                <!-- Stripe Elements will create form elements here -->
            </div>
            
            <button id="submit" style="width: 100%; padding: 12px; background-color: #5469d4; color: white; border: none; border-radius: 4px; font-size: 16px;">
                Pay $10.00
            </button>
        </form>
        
        <div id="payment-result" style="margin-top: 20px;"></div>
    </div>

    <script>
        // Initialize Stripe
        const stripe = Stripe('pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4');
        const elements = stripe.elements();

        // Create card element
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');

        // Handle form submission
        document.getElementById('payment-form').addEventListener('submit', async (event) => {
            event.preventDefault();

            const submitButton = document.getElementById('submit');
            const resultDiv = document.getElementById('payment-result');
            
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';

            // 1. Create payment intent
            const response = await fetch('http://localhost:3000/api/v0/payment/test-payment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    amount: 1000 // $10.00
                })
            });

            const { data } = await response.json();

            // 2. Confirm payment
            const result = await stripe.confirmCardPayment(data.clientSecret, {
                payment_method: {
                    card: cardElement,
                    billing_details: {
                        name: 'Test Customer',
                    },
                }
            });

            if (result.error) {
                resultDiv.innerHTML = `<div style="color: red;">Payment failed: ${result.error.message}</div>`;
            } else {
                resultDiv.innerHTML = `<div style="color: green;">Payment succeeded!</div>`;
            }

            submitButton.disabled = false;
            submitButton.textContent = 'Pay $10.00';
        });
    </script>
</body>
</html>
```

### 5. Test Credit Card Numbers

Use these test card numbers (they won't charge real money):

- **Success:** `4242 4242 4242 4242`
- **Decline:** `4000 0000 0000 0002`
- **Insufficient Funds:** `4000 0000 0000 9995`
- **Expired Card:** `4000 0000 0000 0069`

**Expiry:** Any future date (e.g., 12/25)  
**CVC:** Any 3 digits (e.g., 123)  
**ZIP:** Any 5 digits (e.g., 12345)

### 6. API Endpoints for Testing

**Test Payment (Simple):**
```
POST http://localhost:3000/api/v0/payment/test-payment
Body: { "amount": 1000 }
```

**Custom Payment:**
```
POST http://localhost:3000/api/v0/payment/create-payment-intent
Body: { "amount": 2000, "currency": "usd", "description": "Custom payment" }
```

### 7. Quick Test with Curl

```bash
curl -X POST http://localhost:3000/api/v0/payment/test-payment \
  -H "Content-Type: application/json" \
  -d '{"amount": 1000}'
```

Your backend is ready! Just copy the React component or HTML file to your frontend and start testing! 🚀
