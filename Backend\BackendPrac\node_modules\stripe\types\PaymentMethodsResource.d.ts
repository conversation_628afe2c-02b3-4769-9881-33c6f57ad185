// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    interface PaymentMethodCreateParams {
      /**
       * If this is an `acss_debit` PaymentMethod, this hash contains details about the ACSS Debit payment method.
       */
      acss_debit?: PaymentMethodCreateParams.AcssDebit;

      /**
       * If this is an `affirm` PaymentMethod, this hash contains details about the Affirm payment method.
       */
      affirm?: PaymentMethodCreateParams.Affirm;

      /**
       * If this is an `AfterpayClearpay` PaymentMethod, this hash contains details about the AfterpayClearpay payment method.
       */
      afterpay_clearpay?: PaymentMethodCreateParams.AfterpayClearpay;

      /**
       * If this is an `Alipay` PaymentMethod, this hash contains details about the Alipay payment method.
       */
      alipay?: PaymentMethodCreateParams.Alipay;

      /**
       * This field indicates whether this payment method can be shown again to its customer in a checkout flow. Stripe products such as Checkout and Elements use this field to determine whether a payment method can be shown as a saved payment method in a checkout flow. The field defaults to `unspecified`.
       */
      allow_redisplay?: PaymentMethodCreateParams.AllowRedisplay;

      /**
       * If this is a Alma PaymentMethod, this hash contains details about the Alma payment method.
       */
      alma?: PaymentMethodCreateParams.Alma;

      /**
       * If this is a AmazonPay PaymentMethod, this hash contains details about the AmazonPay payment method.
       */
      amazon_pay?: PaymentMethodCreateParams.AmazonPay;

      /**
       * If this is an `au_becs_debit` PaymentMethod, this hash contains details about the bank account.
       */
      au_becs_debit?: PaymentMethodCreateParams.AuBecsDebit;

      /**
       * If this is a `bacs_debit` PaymentMethod, this hash contains details about the Bacs Direct Debit bank account.
       */
      bacs_debit?: PaymentMethodCreateParams.BacsDebit;

      /**
       * If this is a `bancontact` PaymentMethod, this hash contains details about the Bancontact payment method.
       */
      bancontact?: PaymentMethodCreateParams.Bancontact;

      /**
       * If this is a `billie` PaymentMethod, this hash contains details about the Billie payment method.
       */
      billie?: PaymentMethodCreateParams.Billie;

      /**
       * Billing information associated with the PaymentMethod that may be used or required by particular types of payment methods.
       */
      billing_details?: PaymentMethodCreateParams.BillingDetails;

      /**
       * If this is a `blik` PaymentMethod, this hash contains details about the BLIK payment method.
       */
      blik?: PaymentMethodCreateParams.Blik;

      /**
       * If this is a `boleto` PaymentMethod, this hash contains details about the Boleto payment method.
       */
      boleto?: PaymentMethodCreateParams.Boleto;

      /**
       * If this is a `card` PaymentMethod, this hash contains the user's card details. For backwards compatibility, you can alternatively provide a Stripe token (e.g., for Apple Pay, Amex Express Checkout, or legacy Checkout) into the card hash with format `card: {token: "tok_visa"}`. When providing a card number, you must meet the requirements for [PCI compliance](https://stripe.com/docs/security#validating-pci-compliance). We strongly recommend using Stripe.js instead of interacting with this API directly.
       */
      card?: PaymentMethodCreateParams.Card;

      /**
       * If this is a `cashapp` PaymentMethod, this hash contains details about the Cash App Pay payment method.
       */
      cashapp?: PaymentMethodCreateParams.Cashapp;

      /**
       * The `Customer` to whom the original PaymentMethod is attached.
       */
      customer?: string;

      /**
       * If this is a `customer_balance` PaymentMethod, this hash contains details about the CustomerBalance payment method.
       */
      customer_balance?: PaymentMethodCreateParams.CustomerBalance;

      /**
       * If this is an `eps` PaymentMethod, this hash contains details about the EPS payment method.
       */
      eps?: PaymentMethodCreateParams.Eps;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * If this is an `fpx` PaymentMethod, this hash contains details about the FPX payment method.
       */
      fpx?: PaymentMethodCreateParams.Fpx;

      /**
       * If this is a `giropay` PaymentMethod, this hash contains details about the Giropay payment method.
       */
      giropay?: PaymentMethodCreateParams.Giropay;

      /**
       * If this is a `grabpay` PaymentMethod, this hash contains details about the GrabPay payment method.
       */
      grabpay?: PaymentMethodCreateParams.Grabpay;

      /**
       * If this is an `ideal` PaymentMethod, this hash contains details about the iDEAL payment method.
       */
      ideal?: PaymentMethodCreateParams.Ideal;

      /**
       * If this is an `interac_present` PaymentMethod, this hash contains details about the Interac Present payment method.
       */
      interac_present?: PaymentMethodCreateParams.InteracPresent;

      /**
       * If this is a `kakao_pay` PaymentMethod, this hash contains details about the Kakao Pay payment method.
       */
      kakao_pay?: PaymentMethodCreateParams.KakaoPay;

      /**
       * If this is a `klarna` PaymentMethod, this hash contains details about the Klarna payment method.
       */
      klarna?: PaymentMethodCreateParams.Klarna;

      /**
       * If this is a `konbini` PaymentMethod, this hash contains details about the Konbini payment method.
       */
      konbini?: PaymentMethodCreateParams.Konbini;

      /**
       * If this is a `kr_card` PaymentMethod, this hash contains details about the Korean Card payment method.
       */
      kr_card?: PaymentMethodCreateParams.KrCard;

      /**
       * If this is an `Link` PaymentMethod, this hash contains details about the Link payment method.
       */
      link?: PaymentMethodCreateParams.Link;

      /**
       * Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
       */
      metadata?: Stripe.MetadataParam;

      /**
       * If this is a `mobilepay` PaymentMethod, this hash contains details about the MobilePay payment method.
       */
      mobilepay?: PaymentMethodCreateParams.Mobilepay;

      /**
       * If this is a `multibanco` PaymentMethod, this hash contains details about the Multibanco payment method.
       */
      multibanco?: PaymentMethodCreateParams.Multibanco;

      /**
       * If this is a `naver_pay` PaymentMethod, this hash contains details about the Naver Pay payment method.
       */
      naver_pay?: PaymentMethodCreateParams.NaverPay;

      /**
       * If this is an nz_bank_account PaymentMethod, this hash contains details about the nz_bank_account payment method.
       */
      nz_bank_account?: PaymentMethodCreateParams.NzBankAccount;

      /**
       * If this is an `oxxo` PaymentMethod, this hash contains details about the OXXO payment method.
       */
      oxxo?: PaymentMethodCreateParams.Oxxo;

      /**
       * If this is a `p24` PaymentMethod, this hash contains details about the P24 payment method.
       */
      p24?: PaymentMethodCreateParams.P24;

      /**
       * If this is a `pay_by_bank` PaymentMethod, this hash contains details about the PayByBank payment method.
       */
      pay_by_bank?: PaymentMethodCreateParams.PayByBank;

      /**
       * If this is a `payco` PaymentMethod, this hash contains details about the PAYCO payment method.
       */
      payco?: PaymentMethodCreateParams.Payco;

      /**
       * The PaymentMethod to share.
       */
      payment_method?: string;

      /**
       * If this is a `paynow` PaymentMethod, this hash contains details about the PayNow payment method.
       */
      paynow?: PaymentMethodCreateParams.Paynow;

      /**
       * If this is a `paypal` PaymentMethod, this hash contains details about the PayPal payment method.
       */
      paypal?: PaymentMethodCreateParams.Paypal;

      /**
       * If this is a `pix` PaymentMethod, this hash contains details about the Pix payment method.
       */
      pix?: PaymentMethodCreateParams.Pix;

      /**
       * If this is a `promptpay` PaymentMethod, this hash contains details about the PromptPay payment method.
       */
      promptpay?: PaymentMethodCreateParams.Promptpay;

      /**
       * Options to configure Radar. See [Radar Session](https://stripe.com/docs/radar/radar-session) for more information.
       */
      radar_options?: PaymentMethodCreateParams.RadarOptions;

      /**
       * If this is a `revolut_pay` PaymentMethod, this hash contains details about the Revolut Pay payment method.
       */
      revolut_pay?: PaymentMethodCreateParams.RevolutPay;

      /**
       * If this is a `samsung_pay` PaymentMethod, this hash contains details about the SamsungPay payment method.
       */
      samsung_pay?: PaymentMethodCreateParams.SamsungPay;

      /**
       * If this is a `satispay` PaymentMethod, this hash contains details about the Satispay payment method.
       */
      satispay?: PaymentMethodCreateParams.Satispay;

      /**
       * If this is a `sepa_debit` PaymentMethod, this hash contains details about the SEPA debit bank account.
       */
      sepa_debit?: PaymentMethodCreateParams.SepaDebit;

      /**
       * If this is a `sofort` PaymentMethod, this hash contains details about the SOFORT payment method.
       */
      sofort?: PaymentMethodCreateParams.Sofort;

      /**
       * If this is a `swish` PaymentMethod, this hash contains details about the Swish payment method.
       */
      swish?: PaymentMethodCreateParams.Swish;

      /**
       * If this is a TWINT PaymentMethod, this hash contains details about the TWINT payment method.
       */
      twint?: PaymentMethodCreateParams.Twint;

      /**
       * The type of the PaymentMethod. An additional hash is included on the PaymentMethod with a name matching this value. It contains additional information specific to the PaymentMethod type.
       */
      type?: PaymentMethodCreateParams.Type;

      /**
       * If this is an `us_bank_account` PaymentMethod, this hash contains details about the US bank account payment method.
       */
      us_bank_account?: PaymentMethodCreateParams.UsBankAccount;

      /**
       * If this is an `wechat_pay` PaymentMethod, this hash contains details about the wechat_pay payment method.
       */
      wechat_pay?: PaymentMethodCreateParams.WechatPay;

      /**
       * If this is a `zip` PaymentMethod, this hash contains details about the Zip payment method.
       */
      zip?: PaymentMethodCreateParams.Zip;
    }

    namespace PaymentMethodCreateParams {
      interface AcssDebit {
        /**
         * Customer's bank account number.
         */
        account_number: string;

        /**
         * Institution number of the customer's bank.
         */
        institution_number: string;

        /**
         * Transit number of the customer's bank.
         */
        transit_number: string;
      }

      interface Affirm {}

      interface AfterpayClearpay {}

      interface Alipay {}

      type AllowRedisplay = 'always' | 'limited' | 'unspecified';

      interface Alma {}

      interface AmazonPay {}

      interface AuBecsDebit {
        /**
         * The account number for the bank account.
         */
        account_number: string;

        /**
         * Bank-State-Branch number of the bank account.
         */
        bsb_number: string;
      }

      interface BacsDebit {
        /**
         * Account number of the bank account that the funds will be debited from.
         */
        account_number?: string;

        /**
         * Sort code of the bank account. (e.g., `10-20-30`)
         */
        sort_code?: string;
      }

      interface Bancontact {}

      interface Billie {}

      interface BillingDetails {
        /**
         * Billing address.
         */
        address?: Stripe.Emptyable<Stripe.AddressParam>;

        /**
         * Email address.
         */
        email?: Stripe.Emptyable<string>;

        /**
         * Full name.
         */
        name?: Stripe.Emptyable<string>;

        /**
         * Billing phone number (including extension).
         */
        phone?: Stripe.Emptyable<string>;

        /**
         * Taxpayer identification number. Used only for transactions between LATAM buyers and non-LATAM sellers.
         */
        tax_id?: string;
      }

      interface Blik {}

      interface Boleto {
        /**
         * The tax ID of the customer (CPF for individual consumers or CNPJ for businesses consumers)
         */
        tax_id: string;
      }

      interface Card {
        /**
         * The card's CVC. It is highly recommended to always include this value.
         */
        cvc?: string;

        /**
         * Two-digit number representing the card's expiration month.
         */
        exp_month?: number;

        /**
         * Four-digit number representing the card's expiration year.
         */
        exp_year?: number;

        /**
         * Contains information about card networks used to process the payment.
         */
        networks?: Card.Networks;

        /**
         * The card number, as a string without any separators.
         */
        number?: string;

        /**
         * For backwards compatibility, you can alternatively provide a Stripe token (e.g., for Apple Pay, Amex Express Checkout, or legacy Checkout) into the card hash with format card: {token: "tok_visa"}.
         */
        token?: string;
      }

      namespace Card {
        interface Networks {
          /**
           * The customer's preferred card network for co-branded cards. Supports `cartes_bancaires`, `mastercard`, or `visa`. Selection of a network that does not apply to the card will be stored as `invalid_preference` on the card.
           */
          preferred?: Networks.Preferred;
        }

        namespace Networks {
          type Preferred = 'cartes_bancaires' | 'mastercard' | 'visa';
        }
      }

      interface Cashapp {}

      interface CustomerBalance {}

      interface Eps {
        /**
         * The customer's bank.
         */
        bank?: Eps.Bank;
      }

      namespace Eps {
        type Bank =
          | 'arzte_und_apotheker_bank'
          | 'austrian_anadi_bank_ag'
          | 'bank_austria'
          | 'bankhaus_carl_spangler'
          | 'bankhaus_schelhammer_und_schattera_ag'
          | 'bawag_psk_ag'
          | 'bks_bank_ag'
          | 'brull_kallmus_bank_ag'
          | 'btv_vier_lander_bank'
          | 'capital_bank_grawe_gruppe_ag'
          | 'deutsche_bank_ag'
          | 'dolomitenbank'
          | 'easybank_ag'
          | 'erste_bank_und_sparkassen'
          | 'hypo_alpeadriabank_international_ag'
          | 'hypo_bank_burgenland_aktiengesellschaft'
          | 'hypo_noe_lb_fur_niederosterreich_u_wien'
          | 'hypo_oberosterreich_salzburg_steiermark'
          | 'hypo_tirol_bank_ag'
          | 'hypo_vorarlberg_bank_ag'
          | 'marchfelder_bank'
          | 'oberbank_ag'
          | 'raiffeisen_bankengruppe_osterreich'
          | 'schoellerbank_ag'
          | 'sparda_bank_wien'
          | 'volksbank_gruppe'
          | 'volkskreditbank_ag'
          | 'vr_bank_braunau';
      }

      interface Fpx {
        /**
         * Account holder type for FPX transaction
         */
        account_holder_type?: Fpx.AccountHolderType;

        /**
         * The customer's bank.
         */
        bank: Fpx.Bank;
      }

      namespace Fpx {
        type AccountHolderType = 'company' | 'individual';

        type Bank =
          | 'affin_bank'
          | 'agrobank'
          | 'alliance_bank'
          | 'ambank'
          | 'bank_islam'
          | 'bank_muamalat'
          | 'bank_of_china'
          | 'bank_rakyat'
          | 'bsn'
          | 'cimb'
          | 'deutsche_bank'
          | 'hong_leong_bank'
          | 'hsbc'
          | 'kfh'
          | 'maybank2e'
          | 'maybank2u'
          | 'ocbc'
          | 'pb_enterprise'
          | 'public_bank'
          | 'rhb'
          | 'standard_chartered'
          | 'uob';
      }

      interface Giropay {}

      interface Grabpay {}

      interface Ideal {
        /**
         * The customer's bank. Only use this parameter for existing customers. Don't use it for new customers.
         */
        bank?: Ideal.Bank;
      }

      namespace Ideal {
        type Bank =
          | 'abn_amro'
          | 'asn_bank'
          | 'bunq'
          | 'handelsbanken'
          | 'ing'
          | 'knab'
          | 'moneyou'
          | 'n26'
          | 'nn'
          | 'rabobank'
          | 'regiobank'
          | 'revolut'
          | 'sns_bank'
          | 'triodos_bank'
          | 'van_lanschot'
          | 'yoursafe';
      }

      interface InteracPresent {}

      interface KakaoPay {}

      interface Klarna {
        /**
         * Customer's date of birth
         */
        dob?: Klarna.Dob;
      }

      namespace Klarna {
        interface Dob {
          /**
           * The day of birth, between 1 and 31.
           */
          day: number;

          /**
           * The month of birth, between 1 and 12.
           */
          month: number;

          /**
           * The four-digit year of birth.
           */
          year: number;
        }
      }

      interface Konbini {}

      interface KrCard {}

      interface Link {}

      interface Mobilepay {}

      interface Multibanco {}

      interface NaverPay {
        /**
         * Whether to use Naver Pay points or a card to fund this transaction. If not provided, this defaults to `card`.
         */
        funding?: NaverPay.Funding;
      }

      namespace NaverPay {
        type Funding = 'card' | 'points';
      }

      interface NzBankAccount {
        /**
         * The name on the bank account. Only required if the account holder name is different from the name of the authorized signatory collected in the PaymentMethod's billing details.
         */
        account_holder_name?: string;

        /**
         * The account number for the bank account.
         */
        account_number: string;

        /**
         * The numeric code for the bank account's bank.
         */
        bank_code: string;

        /**
         * The numeric code for the bank account's bank branch.
         */
        branch_code: string;

        reference?: string;

        /**
         * The suffix of the bank account number.
         */
        suffix: string;
      }

      interface Oxxo {}

      interface P24 {
        /**
         * The customer's bank.
         */
        bank?: P24.Bank;
      }

      namespace P24 {
        type Bank =
          | 'alior_bank'
          | 'bank_millennium'
          | 'bank_nowy_bfg_sa'
          | 'bank_pekao_sa'
          | 'banki_spbdzielcze'
          | 'blik'
          | 'bnp_paribas'
          | 'boz'
          | 'citi_handlowy'
          | 'credit_agricole'
          | 'envelobank'
          | 'etransfer_pocztowy24'
          | 'getin_bank'
          | 'ideabank'
          | 'ing'
          | 'inteligo'
          | 'mbank_mtransfer'
          | 'nest_przelew'
          | 'noble_pay'
          | 'pbac_z_ipko'
          | 'plus_bank'
          | 'santander_przelew24'
          | 'tmobile_usbugi_bankowe'
          | 'toyota_bank'
          | 'velobank'
          | 'volkswagen_bank';
      }

      interface PayByBank {}

      interface Payco {}

      interface Paynow {}

      interface Paypal {}

      interface Pix {}

      interface Promptpay {}

      interface RadarOptions {
        /**
         * A [Radar Session](https://stripe.com/docs/radar/radar-session) is a snapshot of the browser metadata and device details that help Radar make more accurate predictions on your payments.
         */
        session?: string;
      }

      interface RevolutPay {}

      interface SamsungPay {}

      interface Satispay {}

      interface SepaDebit {
        /**
         * IBAN of the bank account.
         */
        iban: string;
      }

      interface Sofort {
        /**
         * Two-letter ISO code representing the country the bank account is located in.
         */
        country: Sofort.Country;
      }

      namespace Sofort {
        type Country = 'AT' | 'BE' | 'DE' | 'ES' | 'IT' | 'NL';
      }

      interface Swish {}

      interface Twint {}

      type Type =
        | 'acss_debit'
        | 'affirm'
        | 'afterpay_clearpay'
        | 'alipay'
        | 'alma'
        | 'amazon_pay'
        | 'au_becs_debit'
        | 'bacs_debit'
        | 'bancontact'
        | 'billie'
        | 'blik'
        | 'boleto'
        | 'card'
        | 'cashapp'
        | 'customer_balance'
        | 'eps'
        | 'fpx'
        | 'giropay'
        | 'grabpay'
        | 'ideal'
        | 'kakao_pay'
        | 'klarna'
        | 'konbini'
        | 'kr_card'
        | 'link'
        | 'mobilepay'
        | 'multibanco'
        | 'naver_pay'
        | 'nz_bank_account'
        | 'oxxo'
        | 'p24'
        | 'pay_by_bank'
        | 'payco'
        | 'paynow'
        | 'paypal'
        | 'pix'
        | 'promptpay'
        | 'revolut_pay'
        | 'samsung_pay'
        | 'satispay'
        | 'sepa_debit'
        | 'sofort'
        | 'swish'
        | 'twint'
        | 'us_bank_account'
        | 'wechat_pay'
        | 'zip';

      interface UsBankAccount {
        /**
         * Account holder type: individual or company.
         */
        account_holder_type?: UsBankAccount.AccountHolderType;

        /**
         * Account number of the bank account.
         */
        account_number?: string;

        /**
         * Account type: checkings or savings. Defaults to checking if omitted.
         */
        account_type?: UsBankAccount.AccountType;

        /**
         * The ID of a Financial Connections Account to use as a payment method.
         */
        financial_connections_account?: string;

        /**
         * Routing number of the bank account.
         */
        routing_number?: string;
      }

      namespace UsBankAccount {
        type AccountHolderType = 'company' | 'individual';

        type AccountType = 'checking' | 'savings';
      }

      interface WechatPay {}

      interface Zip {}
    }

    interface PaymentMethodRetrieveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    interface PaymentMethodUpdateParams {
      /**
       * This field indicates whether this payment method can be shown again to its customer in a checkout flow. Stripe products such as Checkout and Elements use this field to determine whether a payment method can be shown as a saved payment method in a checkout flow. The field defaults to `unspecified`.
       */
      allow_redisplay?: PaymentMethodUpdateParams.AllowRedisplay;

      /**
       * Billing information associated with the PaymentMethod that may be used or required by particular types of payment methods.
       */
      billing_details?: PaymentMethodUpdateParams.BillingDetails;

      /**
       * If this is a `card` PaymentMethod, this hash contains the user's card details.
       */
      card?: PaymentMethodUpdateParams.Card;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * If this is an `Link` PaymentMethod, this hash contains details about the Link payment method.
       */
      link?: PaymentMethodUpdateParams.Link;

      /**
       * Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Individual keys can be unset by posting an empty value to them. All keys can be unset by posting an empty value to `metadata`.
       */
      metadata?: Stripe.Emptyable<Stripe.MetadataParam>;

      /**
       * If this is a `pay_by_bank` PaymentMethod, this hash contains details about the PayByBank payment method.
       */
      pay_by_bank?: PaymentMethodUpdateParams.PayByBank;

      /**
       * If this is an `us_bank_account` PaymentMethod, this hash contains details about the US bank account payment method.
       */
      us_bank_account?: PaymentMethodUpdateParams.UsBankAccount;
    }

    namespace PaymentMethodUpdateParams {
      type AllowRedisplay = 'always' | 'limited' | 'unspecified';

      interface BillingDetails {
        /**
         * Billing address.
         */
        address?: Stripe.Emptyable<Stripe.AddressParam>;

        /**
         * Email address.
         */
        email?: Stripe.Emptyable<string>;

        /**
         * Full name.
         */
        name?: Stripe.Emptyable<string>;

        /**
         * Billing phone number (including extension).
         */
        phone?: Stripe.Emptyable<string>;

        /**
         * Taxpayer identification number. Used only for transactions between LATAM buyers and non-LATAM sellers.
         */
        tax_id?: string;
      }

      interface Card {
        /**
         * Two-digit number representing the card's expiration month.
         */
        exp_month?: number;

        /**
         * Four-digit number representing the card's expiration year.
         */
        exp_year?: number;

        /**
         * Contains information about card networks used to process the payment.
         */
        networks?: Card.Networks;
      }

      namespace Card {
        interface Networks {
          /**
           * The customer's preferred card network for co-branded cards. Supports `cartes_bancaires`, `mastercard`, or `visa`. Selection of a network that does not apply to the card will be stored as `invalid_preference` on the card.
           */
          preferred?: Stripe.Emptyable<Networks.Preferred>;
        }

        namespace Networks {
          type Preferred = 'cartes_bancaires' | 'mastercard' | 'visa';
        }
      }

      interface Link {}

      interface PayByBank {}

      interface UsBankAccount {
        /**
         * Bank account holder type.
         */
        account_holder_type?: UsBankAccount.AccountHolderType;

        /**
         * Bank account type.
         */
        account_type?: UsBankAccount.AccountType;
      }

      namespace UsBankAccount {
        type AccountHolderType = 'company' | 'individual';

        type AccountType = 'checking' | 'savings';
      }
    }

    interface PaymentMethodListParams extends PaginationParams {
      /**
       * The ID of the customer whose PaymentMethods will be retrieved.
       */
      customer?: string;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * An optional filter on the list, based on the object `type` field. Without the filter, the list includes all current and future payment method types. If your integration expects only one type of payment method in the response, make sure to provide a type value in the request.
       */
      type?: PaymentMethodListParams.Type;
    }

    namespace PaymentMethodListParams {
      type Type =
        | 'acss_debit'
        | 'affirm'
        | 'afterpay_clearpay'
        | 'alipay'
        | 'alma'
        | 'amazon_pay'
        | 'au_becs_debit'
        | 'bacs_debit'
        | 'bancontact'
        | 'billie'
        | 'blik'
        | 'boleto'
        | 'card'
        | 'cashapp'
        | 'customer_balance'
        | 'eps'
        | 'fpx'
        | 'giropay'
        | 'grabpay'
        | 'ideal'
        | 'kakao_pay'
        | 'klarna'
        | 'konbini'
        | 'kr_card'
        | 'link'
        | 'mobilepay'
        | 'multibanco'
        | 'naver_pay'
        | 'nz_bank_account'
        | 'oxxo'
        | 'p24'
        | 'pay_by_bank'
        | 'payco'
        | 'paynow'
        | 'paypal'
        | 'pix'
        | 'promptpay'
        | 'revolut_pay'
        | 'samsung_pay'
        | 'satispay'
        | 'sepa_debit'
        | 'sofort'
        | 'swish'
        | 'twint'
        | 'us_bank_account'
        | 'wechat_pay'
        | 'zip';
    }

    interface PaymentMethodAttachParams {
      /**
       * The ID of the customer to which to attach the PaymentMethod.
       */
      customer: string;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    interface PaymentMethodDetachParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    class PaymentMethodsResource {
      /**
       * Creates a PaymentMethod object. Read the [Stripe.js reference](https://stripe.com/docs/stripe-js/reference#stripe-create-payment-method) to learn how to create PaymentMethods via Stripe.js.
       *
       * Instead of creating a PaymentMethod directly, we recommend using the [PaymentIntents API to accept a payment immediately or the <a href="/docs/payments/save-and-reuse">SetupIntent](https://stripe.com/docs/payments/accept-a-payment) API to collect payment method details ahead of a future payment.
       */
      create(
        params?: PaymentMethodCreateParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;
      create(
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;

      /**
       * Retrieves a PaymentMethod object attached to the StripeAccount. To retrieve a payment method attached to a Customer, you should use [Retrieve a Customer's PaymentMethods](https://stripe.com/docs/api/payment_methods/customer)
       */
      retrieve(
        id: string,
        params?: PaymentMethodRetrieveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;
      retrieve(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;

      /**
       * Updates a PaymentMethod object. A PaymentMethod must be attached a customer to be updated.
       */
      update(
        id: string,
        params?: PaymentMethodUpdateParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;

      /**
       * Returns a list of PaymentMethods for Treasury flows. If you want to list the PaymentMethods attached to a Customer for payments, you should use the [List a Customer's PaymentMethods](https://stripe.com/docs/api/payment_methods/customer_list) API instead.
       */
      list(
        params?: PaymentMethodListParams,
        options?: RequestOptions
      ): ApiListPromise<Stripe.PaymentMethod>;
      list(options?: RequestOptions): ApiListPromise<Stripe.PaymentMethod>;

      /**
       * Attaches a PaymentMethod object to a Customer.
       *
       * To attach a new PaymentMethod to a customer for future payments, we recommend you use a [SetupIntent](https://stripe.com/docs/api/setup_intents)
       * or a PaymentIntent with [setup_future_usage](https://stripe.com/docs/api/payment_intents/create#create_payment_intent-setup_future_usage).
       * These approaches will perform any necessary steps to set up the PaymentMethod for future payments. Using the /v1/payment_methods/:id/attach
       * endpoint without first using a SetupIntent or PaymentIntent with setup_future_usage does not optimize the PaymentMethod for
       * future use, which makes later declines and payment friction more likely.
       * See [Optimizing cards for future payments](https://stripe.com/docs/payments/payment-intents#future-usage) for more information about setting up
       * future payments.
       *
       * To use this PaymentMethod as the default for invoice or subscription payments,
       * set [invoice_settings.default_payment_method](https://stripe.com/docs/api/customers/update#update_customer-invoice_settings-default_payment_method),
       * on the Customer to the PaymentMethod's ID.
       */
      attach(
        id: string,
        params: PaymentMethodAttachParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;

      /**
       * Detaches a PaymentMethod object from a Customer. After a PaymentMethod is detached, it can no longer be used for a payment or re-attached to a Customer.
       */
      detach(
        id: string,
        params?: PaymentMethodDetachParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;
      detach(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethod>>;
    }
  }
}
