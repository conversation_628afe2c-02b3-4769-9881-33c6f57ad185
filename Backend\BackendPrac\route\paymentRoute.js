// route/paymentRoute.js
const express = require('express');
const router = express.Router();
const {
    createPaymentIntent,
    confirmPayment,
    createCustomer,
    getPaymentHistory,
    handleWebhook
} = require('../controller/paymentController');

// Payment routes
router.post('/create-payment-intent', createPaymentIntent);
router.post('/confirm-payment', confirmPayment);
router.post('/create-customer', createCustomer);
router.get('/payment-history', getPaymentHistory);

// Webhook route (should be raw body, not JSON)
router.post('/webhook', express.raw({ type: 'application/json' }), handleWebhook);

module.exports = router;
