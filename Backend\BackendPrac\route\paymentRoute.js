// route/paymentRoute.js
const express = require('express');
const router = express.Router();
const {
    createPaymentIntent,
    confirmPayment,
    createCustomer,
    getPaymentHistory,
    handleWebhook
} = require('../controller/paymentController');

// Payment routes
router.post('/create-payment-intent', createPaymentIntent);
router.post('/confirm-payment', confirmPayment);
router.post('/create-customer', createCustomer);
router.get('/payment-history', getPaymentHistory);

// Simple test payment endpoint for frontend
router.post('/test-payment', async (req, res) => {
    try {
        const { amount = 1000 } = req.body; // Default $10.00

        const stripe = require('../config/stripe');

        const paymentIntent = await stripe.paymentIntents.create({
            amount: amount,
            currency: 'usd',
            description: 'Test payment from frontend',
            automatic_payment_methods: {
                enabled: true,
            },
        });

        res.status(200).json({
            success: true,
            message: 'Test payment intent created',
            data: {
                clientSecret: paymentIntent.client_secret,
                amount: paymentIntent.amount,
                currency: paymentIntent.currency
            }
        });

    } catch (error) {
        console.error('Test payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Test payment failed',
            error: error.message
        });
    }
});

// Webhook route (should be raw body, not JSON)
router.post('/webhook', express.raw({ type: 'application/json' }), handleWebhook);

module.exports = router;
