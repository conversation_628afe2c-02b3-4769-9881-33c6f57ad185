// utils/Cloudinary.js
const cloudinary = require('cloudinary').v2;
const fs = require('fs');

cloudinary.config({
  cloud_name: process.env.CloudName,
  api_key: process.env.Api_key,
  api_secret: process.env.Api_secret,
});


const UploadCloudinary = async (file) => {
  try {
    if(!file) return null;
    console.log(file);
    const result = await cloudinary.uploader.upload(file, {
      resource_type: 'auto',
      folder: 'images',
    });
    console.log('file uploaded successfully', result.url);
    return result;
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
     fs.unlinkSync(file);
    throw error;
  }
};

module.exports = UploadCloudinary;