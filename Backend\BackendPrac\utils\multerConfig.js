// utils/multerConfig.js
const multer = require('multer');
const path = require('path');

// Define file filter function
const fileFilter = (req, file, cb) => {
  // Check file type
  const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, JPG, PNG, GIF, PDF, DOC, DOCX, and TXT files are allowed.'));
  }
};

// Memory storage configuration (recommended for Cloudinary)
const memoryStorage = multer.memoryStorage();

// Disk storage configuration (alternative option)
const diskStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/'); // Make sure this directory exists
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// Multer configuration for memory storage (recommended for Cloudinary)
const uploadMemory = multer({
  storage: memoryStorage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 5 // Maximum 5 files
  },
  fileFilter: fileFilter
});

// Multer configuration for disk storage
const uploadDisk = multer({
  storage: diskStorage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 5 // Maximum 5 files
  },
  fileFilter: fileFilter
});

// Different upload configurations
const uploadConfigs = {
  // Single file upload
  single: (fieldName) => uploadMemory.single(fieldName),
  
  // Multiple files upload (same field)
  array: (fieldName, maxCount = 5) => uploadMemory.array(fieldName, maxCount),
  
  // Multiple files upload (different fields)
  fields: (fields) => uploadMemory.fields(fields),
  
  // Any files
  any: () => uploadMemory.any(),
  
  // Profile picture upload (specific for user profiles)
  profilePicture: uploadMemory.single('profilePicture'),
  
  // Document upload
  document: uploadMemory.single('document'),
  
  // Multiple images
  images: uploadMemory.array('images', 10)
};

// Error handling middleware
const handleMulterError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 5MB.'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum is 5 files.'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Unexpected field name.'
      });
    }
  }
  
  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  return res.status(500).json({
    success: false,
    message: 'File upload error occurred.'
  });
};

module.exports = {
  uploadMemory,
  uploadDisk,
  uploadConfigs,
  handleMulterError,
  fileFilter
};
